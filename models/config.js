const {t4sDB} = require("../js/database");

class Config {
    constructor() {
        // Initialize with default table name
        this.tableName = 't4s_config';
        this.db = new t4sDB();
    }

    async getConfig() {
        try {
            const query = `SELECT *
                           FROM ${this.tableName}`;
            const result = await this.db.query(query);
            return result.length > 0 ? result[0] : null;
        } catch (error) {
            console.error("Database error in getConfig:", error);
            throw error;
        }
    }

    async updateConfig(obj) {
        let compId = obj.compId;
        try {
            const selectSql = `SELECT *
                               FROM ${this.tableName}`;
            const rows = await this.db.query(selectSql);
            if (rows.length === 0) {
                const insertSql = 'INSERT INTO ' + this.tableName + ' (compid, config) VALUES (' + compId + ', \'' + JSON.stringify(obj) + '\')';
                this.db.query(insertSql);
            } else {
                const updateSql = 'UPDATE ' + this.tableName + ' SET config = \'' + JSON.stringify(obj) + '\', compid = ' + compId;
                this.db.query(updateSql);
            }
            return {success: true};
        } catch (error) {
            console.error("Database error in updateConfig:", error);
            throw error;
        }
    }
}

module.exports = Config;