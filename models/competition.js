const {T4S_TABLE_COMPETITION} = require("../js/dbConstants");
const {t4sDB} = require("../js/database");
const {ROSTER_SYSTEM} = require("../js/serverConstants");
const { updateCallRoom } = require("../controllers/event");
class Competition {
    system = "";
    compName = "";
    orgName = "";
    location = "";
    constructor(compNumber, system) {
        this.compId = compNumber;
        this.db = new t4sDB();
        this.system = ROSTER_SYSTEM;
        if ( global.compObj && global.compObj.compType) {
            this.system = global.compObj.compType;
        }
        if ( typeof system !== "undefined" ) {
            this.system = system;
        }
    }

    async updatelocation(compInfo) {
        // get the competition record and if no location, update from the location
        let sql = `SELECT location FROM ` + T4S_TABLE_COMPETITION + ` WHERE compid = ${this.compId}`;
        let result = await this.db.query(sql);
        if ( result === null || result.length === 0 ) {
            this.updateInfo(compInfo);
        }else if (result[0].location === null || result[0].location === "") {
            sql = `UPDATE ` + T4S_TABLE_COMPETITION + ` SET location = '${compInfo.compLocation}' WHERE compid = ${this.compId}`;
            this.db.query(sql);
        }
    }
    e4sUpdateInfo(info){
        console.log("Competition: e4sUpdateInfo");
        let fieldName = "";
        let fieldValue = "";
        let fieldInfo = "";
        let fieldOrgName = "";
        let fieldOrgValue = "";
        let fieldOrgInfo = "";
        let fieldLocName = "";
        let fieldLocValue = "";
        let fieldLocInfo = "";
        let fieldEventName = "";
        let fieldEventValue = "";
        let fieldEventInfo = "";
        let fieldOptionsName = "";
        let fieldOptionsValue = "";
        let fieldOptionsInfo = "";
        if ( info.name ) {
            this.compName = info.name;
            fieldName = "compName,";
            fieldValue = '"' + this.compName + '",';
            fieldInfo = "compName = '" + this.compName + "',";
            global.compObj.compName = this.compName;
        }
        if ( info.club ) {
            this.orgName = info.club;
            fieldOrgName = "organiser,";
            fieldOrgValue = '"' + this.orgName + '",';
            fieldOrgInfo = "organiser = '" + this.orgName + "',";
        }

        if ( info.location ) {
            this.location = info.location;
            fieldLocName = "location,";
            fieldLocValue = '"' + this.location + '",';
            fieldLocInfo = "location = '" + this.location + "',";
        }
        info.eventNameFormat = "{{EVENTNAME}}";
        if ( info.eventNameFormat ){
            fieldEventName = "eventNameFormat,";
            fieldEventValue = '"' + info.eventNameFormat + '",';
            fieldEventInfo = "eventNameFormat = '" + info.eventNameFormat + "',";
        }
        if ( info.optionsXXX ){
            global.compObj.options = info.options;
            const options = JSON.stringify(info.options);
            fieldOptionsName = "options,";
            fieldOptionsValue = `'` + options + `',`;
            fieldOptionsInfo = `options = '` + options + `',`;
        }

        let compId = this.compId;
        if ( compId > 0 ) {
            let sql = `INSERT INTO ` + T4S_TABLE_COMPETITION + ` (compid, ` + fieldName + fieldOrgName + fieldLocName + fieldEventName + fieldOptionsName + ` compType) 
                            VALUES (${compId}, ` + fieldValue + fieldOrgValue + fieldLocValue + fieldEventValue + fieldOptionsValue + `'${this.system}') 
                            ON DUPLICATE KEY UPDATE ` + fieldInfo + fieldOrgInfo + fieldLocInfo + fieldEventInfo + fieldOptionsInfo + ` compType = '${this.system}'`;
            this.db.query(sql);
            const { updateConfig } = require("../controllers/config");
            updateConfig(global.compObj);
        }else{
            console.log("Invalid Competition Id");
        }
    }
    updateInfo(info) {
        console.log("Competition: updateInfo");
        let fieldName = "";
        let fieldValue = "";
        let fieldInfo = "";
        let fieldOrgName = "";
        let fieldOrgValue = "";
        let fieldOrgInfo = "";
        let fieldLocName = "";
        let fieldLocValue = "";
        let fieldLocInfo = "";
        let fieldEventName = "";
        let fieldEventValue = "";
        let fieldEventInfo = "";
        let fieldOptionsName = "";
        let fieldOptionsValue = "";
        let fieldOptionsInfo = "";
        if ( info.name ) {
            this.compName = info.name;
            fieldName = "compName,";
            fieldValue = '"' + this.compName + '",';
            fieldInfo = "compName = '" + this.compName + "',";
            global.compObj.compName = this.compName;
        }
        if ( info.organiser ) {
            this.orgName = info.organiser;
            fieldOrgName = "organiser,";
            fieldOrgValue = '"' + this.orgName + '",';
            fieldOrgInfo = "organiser = '" + this.orgName + "',";
        }
        if ( info.compLocation ) {
            this.location = info.compLocation;
            fieldLocName = "location,";
            fieldLocValue = '"' + this.location + '",';
            fieldLocInfo = "location = '" + this.location + "',";
        }
        if ( info.eventNameFormat ){
            fieldEventName = "eventNameFormat,";
            fieldEventValue = '"' + info.eventNameFormat + '",';
            fieldEventInfo = "eventNameFormat = '" + info.eventNameFormat + "',";
        }
        if ( info.options ){
            global.compObj.options = info.options;
            const options = JSON.stringify(info.options);
            fieldOptionsName = "options,";
            fieldOptionsValue = `'` + options + `',`;
            fieldOptionsInfo = `options = '` + options + `',`;
        }
        this.system = "Roster"; // irelevant, set on constructor
        if ( info.compType ){
            this.system = info.compType;
        }
        let compId = parseInt(this.compId);
        if ( compId > 0 ) {
            global.compObj.compId = compId;
            let sql = `INSERT INTO ` + T4S_TABLE_COMPETITION + ` (compid, ` + fieldName + fieldOrgName + fieldLocName + fieldEventName + fieldOptionsName + ` compType) 
                            VALUES (${compId}, ` + fieldValue + fieldOrgValue + fieldLocValue + fieldEventValue + fieldOptionsValue + `'${this.system}') 
                            ON DUPLICATE KEY UPDATE ` + fieldInfo + fieldOrgInfo + fieldLocInfo + fieldEventInfo + fieldOptionsInfo + ` compType = '${this.system}'`;
            this.db.query(sql);
            const { updateConfig } = require("../controllers/config");
            updateConfig(global.compObj);
        }else{
            console.log("Invalid Competition Id");
        }
    }
    async getInfo() {
        let sql = `SELECT c.*, conf.config FROM ` + T4S_TABLE_COMPETITION + ` c, ` + T4S_TABLE_CONFIG + ` conf WHERE c.compid = conf.compid AND c.compid = ${this.compId}`;
        let result = await this.db.query(sql);
        if (result.length === 0) {
            return null;
        }
        result[0].options = JSON.parse(result[0].options);
        let config = JSON.parse(result[0].config);
        // remove config from result[0]
        delete (result[0].config);
        // merge config into result[0]
        let compInfo = {...result[0], ...config};
        global.compObj = compInfo;
        global.compObj.compId = this.compId;
        delete (global.compObj.compid);
        return result;
    }
}
module.exports = { Competition };
